import {
  Body,
  Controller,
  Header,
  Headers,
  HttpCode,
  Post,
  Query,
  Req,
  Res
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { LogLevel } from 'ads-layouts-tools';
import { FastifyReply, FastifyRequest } from 'fastify';
import { getClientIp } from 'Helpers';
import {
  BodyToHeaderParse,
  CommonRequest,
  ContentMeta,
  GeneratorHeaders,
  GeneratorQuery,
  IDebugOptions
} from 'InterfacesAndTypes';
import { CreateException, log, tracerUtils } from 'Utils';
import { CacheService } from '../cacheModule/cache.service';
import { ENV } from '../envalidConfig';
import { runHeadersValidation } from './dto/headersValidation';
import { RequestBodyValidationPipe } from './dto/requestBodyValidation.pipe';
import { GeneratorService } from './generator.service';
import { GetGenerateDoc } from './swagger/generator.doc.decorator';

const extractHeaders = (headers: GeneratorHeaders): BodyToHeaderParse<ContentMeta> => {
  const mappedHeaders: BodyToHeaderParse<ContentMeta> = {
    'x-at-access-model': headers['x-at-access-model'],
    'x-at-device-type': headers['x-at-device-type'],
    'x-at-location-info-page-id': headers['x-at-location-info-page-id'],
    'x-at-location-info-page-type': headers['x-at-location-info-page-type'],
    'x-at-location-info-section-id': headers['x-at-location-info-section-id'],
    'x-at-location-info-section-name': headers['x-at-location-info-section-name'],
    'x-at-service-env': headers['x-at-service-env'],
    'x-at-service-id': headers['x-at-service-id'],
    'x-at-site-version': headers['x-at-site-version'],
    'x-at-site-version-identifier': headers['x-at-site-version-identifier'],
    'x-at-paywall': headers['x-at-paywall'],
    'x-at-rules-package': headers['x-at-rules-package'],
    'x-at-time': headers['x-at-time']
  };

  return mappedHeaders;
};

@Controller('generator')
@ApiTags('Generator')
export class GeneratorController {
  constructor(
    private generatorService: GeneratorService,
    private readonly cache: CacheService
  ) {}

  @Post()
  @GetGenerateDoc()
  @HttpCode(200)
  @Header('Cache-Control', ENV.CACHE_CONTROL)
  async generate(
    @Req() req: FastifyRequest,
    @Body(RequestBodyValidationPipe) body: CommonRequest,
    @Query() queryParams: GeneratorQuery,
    @Res() res: FastifyReply,
    @Headers() headers: GeneratorHeaders
  ): Promise<FastifyReply> {
    const traceId = tracerUtils.getActiveTraceId();
    const span = tracerUtils.getActiveSpan();

    log(
      'GENERATOR_REQUEST',
      {
        reqHeaders: headers,
        reqIP: getClientIp(req),
        body: JSON.stringify(body)
      },
      LogLevel.dev
    );

    try {
      const debugOptions: IDebugOptions = this.generatorService.retrieveDebugQueryParamOptions(
        queryParams.debug
      );

      if (!debugOptions.debug && ENV.HEADERS_VALIDATION === 'ENABLED') {
        const contentMetaHeaders = extractHeaders(headers);
        runHeadersValidation(body.meta, contentMetaHeaders);
      }

      if (span !== null) {
        span.setTag('tag-bodyMeta', body.meta);
      }

      const appCacheHeaders = this.cache.getAppCacheHeaders();

      const mapRedir = body.mapRedir;
      if (mapRedir) {
        log('MAP_REDIR', { mapRedir }, LogLevel.dev);
      }

      void res.headers({
        traceId,
        ...appCacheHeaders
      });

      const response = await this.generatorService.createResponse(
        body,
        debugOptions.debug,
        debugOptions.omitCache,
        mapRedir
      );

      return res.send(response);
    } catch (e) {
      throw CreateException(e as any, undefined, '_CONTROLLER');
    }
  }
}
