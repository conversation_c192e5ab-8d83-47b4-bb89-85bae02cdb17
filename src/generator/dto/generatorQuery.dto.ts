import * as Jo<PERSON> from 'joi';
import { PipeTransform, Injectable } from '@nestjs/common';
import { GeneratorQueryInput, GeneratorQueryOutput } from 'InterfacesAndTypes';

const mapRedirPattern =
  /^(?<prefix>.+\/)sdk-display\/configs\/[\w]+\/[\d\.]+\/(?<serviceId>\w+)_map.json$/;

const schema = Joi.object<GeneratorQueryInput>({
  debug: Joi.boolean().default(false),
  mapRedirUrl: Joi.string().regex(mapRedirPattern).optional()
});

@Injectable()
export class GeneratorQueryPipe
  implements PipeTransform<GeneratorQueryInput, GeneratorQueryOutput>
{
  transform(passedValue: GeneratorQueryInput): GeneratorQueryOutput {
    const { error, value } = schema.validate(passedValue) as {
      error: Joi.ValidationError | null;
      value: GeneratorQueryInput;
    };

    if (error) {
      throw new Error(`Validation failed: ${error.message}`);
    }

    if (!value.mapRedirUrl) {
      return {
        debug: value.debug!
      };
    }

    const match = value.mapRedirUrl.match(mapRedirPattern);

    return {
      debug: value.debug!,
      mapRedir: {
        url: value.mapRedirUrl,
        prefix: match!.groups!.prefix,
        serviceId: match!.groups!.serviceId
      }
    };
  }
}
