{"name": "adsLayoutWorker", "version": "1.51.4-test", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prepare": "husky", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,test}/**/*.ts\"", "lint:fix": "npm run lint -- --fix", "prettier": "npx prettier src test --check", "prettier:fix": "npm run prettier -- --write", "format": "npm run prettier:fix && npm run lint:fix", "test": "jest --silent", "test:gh": "jest", "test:logs": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@fastify/helmet": "^11.1.1", "@fastify/static": "^6.12.0", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^10.4.15", "@nestjs/core": "^10.4.15", "@nestjs/mapped-types": "^2.0.6", "@nestjs/mongoose": "^10.1.0", "@nestjs/platform-fastify": "^10.4.15", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^8.1.0", "@nestjs/throttler": "^6.4.0", "ads-layouts-tools": "^1.6.0", "cache-manager": "^5.7.6", "dayjs": "^1.11.13", "dd-trace": "^4.52.0", "dotenv": "^16.4.7", "envalid": "^8.0.0", "fastify-swagger": "^5.2.0", "joi": "^17.13.3", "json-rules-engine": "^7.3.0", "lodash": "^4.17.21", "mongodb-memory-server": "^10.1.3", "mongoose": "^7.8.6", "nestjs-joi": "^1.10.1", "node-color-log": "^12.0.1", "node-fetch": "^2.7.0", "reflect-metadata": "^0.1.13", "rimraf": "^5.0.10", "rxjs": "^7.8.1", "utility": "^1.18.0", "uuid": "^9.0.1"}, "devDependencies": {"@golevelup/ts-jest": "^0.6.2", "@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.15", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.13", "@types/node": "^22.10.2", "@types/node-fetch": "^2.6.13", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-unused-imports": "^3.2.0", "husky": "^9.1.7", "jest": "^29.7.0", "prettier": "^3.4.2", "supertest": "^6.3.4", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "type-fest": "^4.40.1", "typescript": "^5.7.2"}, "engines": {"node": ">=22.0.0"}}