import { DisplayConfig } from 'ads-layouts-tools';
import { matchDisplayConfigEnv } from 'src/displayConfig/helpers/matchDisplayConfigEnv';

jest.mock('Utils/logger');

describe('match display config env test suite', () => {
  test('is a function', () => {
    expect(typeof matchDisplayConfigEnv).toEqual('function');
  });

  const data = [
    { env: 'default' },
    { env: 'prev-(1|2|3|4|5|6|7|8)' },
    { env: 'prev-9' }
  ] as DisplayConfig[];

  const dataRepeatedEnv = [
    { env: 'default' },
    { env: 'prev-(1|2|3|4|5|6|7|8)' },
    { env: 'prev-11' },
    { env: 'prev-11' }
  ] as DisplayConfig[];

  test('returns exact match string', () => {
    expect(matchDisplayConfigEnv('default', data)).toEqual([{ env: 'default' }]);
    expect(matchDisplayConfigEnv('prev-9', data)).toEqual([{ env: 'prev-9' }]);
  });

  test('returns default if cannot match', () => {
    expect(matchDisplayConfigEnv('default', data)).toEqual([{ env: 'default' }]);
    expect(matchDisplayConfigEnv('foo', data)).toEqual([{ env: 'default' }]);
    expect(matchDisplayConfigEnv('prev-10', data)).toEqual([{ env: 'default' }]);
    expect(matchDisplayConfigEnv('prev-11', data)).toEqual([{ env: 'default' }]);
  });

  test('returns matched version', () => {
    expect(matchDisplayConfigEnv('prev-1', data)).toEqual([{ env: 'prev-(1|2|3|4|5|6|7|8)' }]);
    expect(matchDisplayConfigEnv('prev-4', data)).toEqual([{ env: 'prev-(1|2|3|4|5|6|7|8)' }]);
    expect(matchDisplayConfigEnv('prev-8', data)).toEqual([{ env: 'prev-(1|2|3|4|5|6|7|8)' }]);
  });

  test('returns correct object for repeated envs', () => {
    expect(matchDisplayConfigEnv('prev-11', dataRepeatedEnv)).toHaveLength(2);
  });

  test('returns default for not provided env', () => {
    expect(matchDisplayConfigEnv('prev-11', dataRepeatedEnv)).toHaveLength(2);
  });

  it('should throw error if empty array provided', () => {
    expect(() => matchDisplayConfigEnv('default', [])).toThrow();
  });
});
