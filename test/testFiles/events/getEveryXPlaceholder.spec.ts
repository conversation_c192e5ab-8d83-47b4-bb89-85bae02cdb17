import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import {
  Event,
  ExcludeFactsModel,
  PlaceholderPositionEnum,
  VariantWeightsConfig,
  VariantWeightsConfigSchema
} from 'ads-layouts-tools';
import { FactType } from 'InterfacesAndTypes';
import { facts, facts2, variantConfigDummy } from 'Mocks';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { connect, Connection, Model } from 'mongoose';
import { CacheModule } from 'src/cacheModule/cache.module';
import { EventsService } from 'src/events/events.service';
import { GetEveryXPlaceholderClass } from 'src/events/getPlaceholderHelpers/GetEveryXPlaceholder.class';
import { VariantWeightsConfigService } from 'src/variantWeightsConfig/variantWeightsConfig.service';

jest.mock('Utils/logger');

describe('getEveryXPlaceholder test suite', () => {
  let mongod: MongoMemoryServer;
  let mongoConnection: Connection;
  let everyXPlaceholderHelper: GetEveryXPlaceholderClass;
  let mongoVariantsConfigModel: Model<VariantWeightsConfig>;

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();
    mongoConnection = (await connect(uri)).connection;

    mongoVariantsConfigModel = mongoConnection.model(
      VariantWeightsConfig.name,
      VariantWeightsConfigSchema
    );

    const app: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register({ isActive: false })],
      providers: [
        EventsService,
        VariantWeightsConfigService,
        {
          provide: getModelToken(VariantWeightsConfig.name),
          useValue: mongoVariantsConfigModel
        }
      ]
    }).compile();

    everyXPlaceholderHelper = new GetEveryXPlaceholderClass();
  });

  beforeEach(async () => {
    await mongoVariantsConfigModel.insertMany(variantConfigDummy);
  });

  afterEach(async () => {
    await mongoVariantsConfigModel.deleteMany({});
    jest.restoreAllMocks();
  });

  afterAll(async () => {
    await mongoConnection.dropDatabase();
    await mongoConnection.close();
    await mongod.stop();
  });

  describe('every', () => {
    it('should return every 3rd placeholder when every = 3', () => {
      const event = {
        params: {
          placeholder: {
            every: 3
          }
        }
      } as Event;

      const result = everyXPlaceholderHelper.get(event, facts);

      expect(result).toEqual([
        { id: '5', type: 'placeholder' },
        { id: '11', type: 'placeholder' },
        { id: '17', type: 'placeholder' },
        { id: '23', type: 'placeholder' },
        { id: '29', type: 'placeholder' }
      ]);
    });

    it('should return an empty array when every parameter is larger than total placeholders', () => {
      const event = {
        params: {
          placeholder: {
            every: 50
          }
        }
      } as Event;

      const result = everyXPlaceholderHelper.get(event, facts);

      expect(result).toEqual([]);
    });

    it('should return empty array when fact array is empty', () => {
      const event = {
        params: {
          placeholder: {
            every: 1
          }
        }
      } as Event;

      const facts: FactType[] = [];

      const result = everyXPlaceholderHelper.get(event, facts);

      expect(result).toEqual([]);
    });
  });

  describe('max', () => {
    it('should limit the number of placeholders to max when max is provided', () => {
      const event = {
        params: {
          placeholder: {
            every: 1,
            max: 2
          }
        }
      } as Event;

      const result = everyXPlaceholderHelper.get(event, facts);

      expect(result).toEqual([
        { id: '1', type: 'placeholder' },
        { id: '3', type: 'placeholder' }
      ]);
    });

    it('should return all placeholders per Fact, when max is larger than available facts', () => {
      const event = {
        params: {
          placeholder: {
            every: 1,
            max: 50
          }
        }
      } as Event;

      const result = everyXPlaceholderHelper.get(event, facts);

      expect(result).toEqual([
        { id: '1', type: 'placeholder' },
        { id: '3', type: 'placeholder' },
        { id: '5', type: 'placeholder' },
        { id: '7', type: 'placeholder' },
        { id: '9', type: 'placeholder' },
        { id: '11', type: 'placeholder' },
        { id: '13', type: 'placeholder' },
        { id: '15', type: 'placeholder' },
        { id: '17', type: 'placeholder' },
        { id: '19', type: 'placeholder' },
        { id: '21', type: 'placeholder' },
        { id: '23', type: 'placeholder' },
        { id: '25', type: 'placeholder' },
        { id: '27', type: 'placeholder' },
        { id: '29', type: 'placeholder' },
        { id: '31', type: 'placeholder' },
        { id: '33', type: 'placeholder' }
      ]);
    });
  });

  describe('startIndex', () => {
    it('should matter whether startIndex is defined if position is not defined', () => {
      const event = {
        params: {
          placeholder: {
            every: 1,
            startIndex: 50
          }
        }
      } as Event;

      const result = everyXPlaceholderHelper.get(event, facts);

      expect(result).toEqual([]);
    });

    it('should not include placeholders before startIndex when startIndex is set', () => {
      const event = {
        params: {
          placeholder: {
            every: 2,
            position: PlaceholderPositionEnum.UNDER,
            startIndex: 10
          }
        }
      } as Event;

      const result = everyXPlaceholderHelper.get(event, facts);

      expect(result).toEqual([
        { id: '23', type: 'placeholder' },
        { id: '27', type: 'placeholder' },
        { id: '31', type: 'placeholder' },
        { id: '35', type: 'placeholder' }
      ]);
    });
  });

  describe('element', () => {
    it('should filter out placeholders based on element type', () => {
      const event = {
        params: {
          placeholder: {
            every: 1,
            element: ['photo'],
            position: PlaceholderPositionEnum.ABOVE
          }
        }
      } as Event;

      const result = everyXPlaceholderHelper.get(event, facts);

      expect(result).toEqual([
        { id: '9', type: 'placeholder' },
        { id: '29', type: 'placeholder' }
      ]);
    });
  });

  describe('omitLast', () => {
    it('should return an empty array when single element fact array and omitLast is true', () => {
      const event = {
        params: {
          placeholder: {
            every: 1,
            ommitLast: true
          }
        }
      } as Event;

      const facts = [{ id: '1', type: 'placeholder' }] as FactType[];

      const result = everyXPlaceholderHelper.get(event, facts);

      expect(result).toEqual([]);
    });

    it('should omit the last placeholder when ommitLast is true', () => {
      const event = {
        params: {
          placeholder: {
            ommitLast: true,
            every: 1
          }
        }
      } as Event;

      const facts = [
        { id: '1', type: 'placeholder' },
        { id: '2', type: 'text' },
        { id: '3', type: 'placeholder' }
      ] as FactType[];

      const result = everyXPlaceholderHelper.get(event, facts);

      expect(result).toEqual([{ id: '1', type: 'placeholder' }]);
    });
  });

  describe('position', () => {
    it('should return placeholders above specified element type when position is ABOVE', () => {
      const event = {
        params: {
          placeholder: {
            every: 1,
            position: PlaceholderPositionEnum.ABOVE,
            element: ['text']
          }
        }
      } as Event;

      const facts = [
        { id: '1', type: 'placeholder' },
        { id: '2', type: 'text' },
        { id: '3', type: 'placeholder' },
        { id: '4', type: 'text' },
        { id: '5', type: 'placeholder' },
        { id: '6', type: 'text' },
        { id: '7', type: 'placeholder' }
      ] as FactType[];

      const result = everyXPlaceholderHelper.get(event, facts);

      expect(result).toEqual([
        { id: '1', type: 'placeholder' },
        { id: '3', type: 'placeholder' },
        { id: '5', type: 'placeholder' }
      ]);
    });

    it('should return placeholders under specified element type when position is UNDER', () => {
      const event = {
        params: {
          placeholder: {
            every: 1,
            position: PlaceholderPositionEnum.UNDER,
            element: ['text']
          }
        }
      } as Event;

      const facts = [
        { id: '1', type: 'placeholder' },
        { id: '2', type: 'text' },
        { id: '3', type: 'placeholder' },
        { id: '4', type: 'text' },
        { id: '5', type: 'placeholder' },
        { id: '6', type: 'text' },
        { id: '7', type: 'placeholder' }
      ] as FactType[];

      const result = everyXPlaceholderHelper.get(event, facts);

      expect(result).toEqual([
        { id: '3', type: 'placeholder' },
        { id: '5', type: 'placeholder' },
        { id: '7', type: 'placeholder' }
      ]);
    });
  });

  describe('exclude', () => {
    describe('empty omitTypes and empty skipOverFactNames - sanity check', () => {
      it('should return all placeholders when omitTypes and skipOverFactNames are empty - UNDER', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.UNDER,
              exclude: { omittedFactNames: [], skipOverFactNames: [] } as ExcludeFactsModel
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts2);

        expect(result).toEqual([
          { id: '5', type: 'placeholder' },
          { id: '9', type: 'placeholder' },
          { id: '13', type: 'placeholder' },
          { id: '17', type: 'placeholder' },
          { id: '21', type: 'placeholder' },
          { id: '25', type: 'placeholder' }
        ]);
      });

      it('should return all placeholders when omitTypes and skipOverFactNames are empty - ABOVE', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.ABOVE,
              exclude: { omittedFactNames: [], skipOverFactNames: [] } as ExcludeFactsModel
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts2);

        expect(result).toEqual([
          { id: '4', type: 'placeholder' },
          { id: '8', type: 'placeholder' },
          { id: '12', type: 'placeholder' },
          { id: '16', type: 'placeholder' },
          { id: '20', type: 'placeholder' },
          { id: '24', type: 'placeholder' }
        ]);
      });
    });

    describe('empty omitTypes and non-empty skipOver', () => {
      it('should skipOverFactNames excluded facts based on provided fact names - UNDER', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.UNDER,
              exclude: {
                omittedFactNames: [],
                skipOverFactNames: ['subhead']
              } as ExcludeFactsModel
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts2);

        expect(result).toEqual([
          { id: '6', type: 'placeholder' },
          { id: '10', type: 'placeholder' },
          { id: '16', type: 'placeholder' },
          { id: '20', type: 'placeholder' },
          { id: '25', type: 'placeholder' }
        ]);
      });

      it('should skipOverFactNames excluded facts based on provided fact names - ABOVE', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.ABOVE,
              exclude: {
                omittedFactNames: [],
                skipOverFactNames: ['subhead']
              } as ExcludeFactsModel
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts2);

        expect(result).toEqual([
          { id: '5', type: 'placeholder' },
          { id: '9', type: 'placeholder' },
          { id: '15', type: 'placeholder' },
          { id: '19', type: 'placeholder' },
          { id: '24', type: 'placeholder' }
        ]);
      });
    });

    describe('non-empty omitTypes and empty skipOverFactNames', () => {
      it('should omit placeholders based on provided fact names as omittedFactNames - UNDER - 1', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.UNDER,
              exclude: {
                omittedFactNames: ['photo', 'html'],
                skipOverFactNames: []
              } as ExcludeFactsModel
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts);

        expect(result).toEqual([
          { id: '13', type: 'placeholder' },
          { id: '25', type: 'placeholder' },
          { id: '33', type: 'placeholder' }
        ]);
      });

      it('should omit placeholders based on provided fact names as omittedFactNames - ABOVE - 1', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.ABOVE,
              exclude: {
                omittedFactNames: ['photo', 'html'],
                skipOverFactNames: []
              } as ExcludeFactsModel
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts);

        expect(result).toEqual([
          { id: '7', type: 'placeholder' },
          { id: '15', type: 'placeholder' },
          { id: '25', type: 'placeholder' },
          { id: '33', type: 'placeholder' }
        ]);
      });

      it('should omit placeholders based on provided fact names as omittedFactNames - UNDER - 2', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.UNDER,
              exclude: {
                omittedFactNames: ['teaser', 'subhead'],
                skipOverFactNames: []
              } as ExcludeFactsModel
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts2);

        expect(result).toEqual([
          { id: '6', type: 'placeholder' },
          { id: '12', type: 'placeholder' },
          { id: '16', type: 'placeholder' },
          { id: '22', type: 'placeholder' },
          { id: '26', type: 'placeholder' }
        ]);
      });

      it('should omit placeholders based on provided fact names as omittedFactNames - ABOVE - 2', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.ABOVE,
              exclude: {
                omittedFactNames: ['teaser', 'subhead'],
                skipOverFactNames: []
              } as ExcludeFactsModel
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts2);

        expect(result).toEqual([
          { id: '6', type: 'placeholder' },
          { id: '9', type: 'placeholder' },
          { id: '15', type: 'placeholder' },
          { id: '19', type: 'placeholder' },
          { id: '22', type: 'placeholder' }
        ]);
      });
    });

    describe('non-empty omitTypes and non-empty skipOverFactNames', () => {
      it('should exclude placeholders and facts based on omittedFactNames and skipOver - UNDER - equal arrays', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.UNDER,
              exclude: {
                omittedFactNames: ['photo', 'html'],
                skipOverFactNames: ['photo', 'html']
              }
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts);

        expect(result).toEqual([
          { id: '13', type: 'placeholder' },
          { id: '25', type: 'placeholder' }
        ]);
      });

      it('should exclude placeholders and facts based on omittedFactNames and skipOver - ABOVE - equal arrays', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.ABOVE,
              exclude: {
                omittedFactNames: ['photo', 'html'],
                skipOverFactNames: ['photo', 'html']
              }
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts);

        expect(result).toEqual([
          { id: '7', type: 'placeholder' },
          { id: '17', type: 'placeholder' },
          { id: '33', type: 'placeholder' }
        ]);
      });

      it('should omit and skipOverFactNames excluded facts based on provided fact names - UNDER - skipOverFactNames shorter than omitted', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.UNDER,
              exclude: {
                omittedFactNames: ['teaser', 'subhead'],
                skipOverFactNames: ['subhead']
              }
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts2);

        expect(result).toEqual([
          { id: '6', type: 'placeholder' },
          { id: '12', type: 'placeholder' },
          { id: '19', type: 'placeholder' },
          { id: '26', type: 'placeholder' }
        ]);
      });

      it('should omit and skipOverFactNames excluded facts based on provided fact names - ABOVE - skipOverFactNames shorter than omitted', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.ABOVE,
              exclude: {
                omittedFactNames: ['teaser', 'subhead'],
                skipOverFactNames: ['subhead']
              }
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts2);

        expect(result).toEqual([
          { id: '6', type: 'placeholder' },
          { id: '9', type: 'placeholder' },
          { id: '15', type: 'placeholder' },
          { id: '19', type: 'placeholder' },
          { id: '26', type: 'placeholder' }
        ]);
      });

      it('should omit and skipOver excluded facts based on provided fact names - UNDER - omitted shorter than skipOverFactNames', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.UNDER,
              exclude: {
                omittedFactNames: ['subhead'],
                skipOverFactNames: ['teaser', 'subhead']
              }
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts2);

        expect(result).toEqual([
          { id: '6', type: 'placeholder' },
          { id: '12', type: 'placeholder' },
          { id: '17', type: 'placeholder' },
          { id: '23', type: 'placeholder' }
        ]);
      });

      it('should omit and skipOver excluded facts based on provided fact names - ABOVE - omitted shorter than skipOverFactNames', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.ABOVE,
              exclude: {
                omittedFactNames: ['subhead'],
                skipOverFactNames: ['teaser', 'subhead']
              }
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts2);

        expect(result).toEqual([
          { id: '6', type: 'placeholder' },
          { id: '9', type: 'placeholder' },
          { id: '15', type: 'placeholder' },
          { id: '22', type: 'placeholder' }
        ]);
      });

      it('should omit and skipOver excluded facts based on provided fact names - UNDER - no overlap between omitted and skipOverFactNames', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.UNDER,
              exclude: { omittedFactNames: ['teaser'], skipOverFactNames: ['subhead'] }
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts2);

        expect(result).toEqual([
          { id: '6', type: 'placeholder' },
          { id: '10', type: 'placeholder' },
          { id: '16', type: 'placeholder' },
          { id: '20', type: 'placeholder' },
          { id: '26', type: 'placeholder' }
        ]);
      });
      it('should omit and skipOver excluded facts based on provided fact names - ABOVE - no overlap between omitted and skipOverFactNames', () => {
        const event = {
          params: {
            placeholder: {
              every: 4,
              position: PlaceholderPositionEnum.ABOVE,
              exclude: { omittedFactNames: ['teaser'], skipOverFactNames: ['subhead'] }
            }
          }
        } as Event;

        const result = everyXPlaceholderHelper.get(event, facts2);

        expect(result).toEqual([
          { id: '5', type: 'placeholder' },
          { id: '9', type: 'placeholder' },
          { id: '15', type: 'placeholder' },
          { id: '19', type: 'placeholder' },
          { id: '26', type: 'placeholder' }
        ]);
      });
    });
  });

  describe('edge cases', () => {
    it('should return an empty array when facts array is empty', () => {
      const event = {
        params: {
          placeholder: {
            every: 1,
            ommitLast: true
          }
        }
      } as Event;

      const result = everyXPlaceholderHelper.get(event, []);

      expect(result).toEqual([]);
    });

    it('should prevent duplicate placeholders', () => {
      const event = {
        params: {
          placeholder: {
            position: 'above',
            every: 1,
            ommitLast: true,
            exclude: {
              omittedFactNames: ['photo', 'html'],
              skipOverFactNames: []
            }
          }
        }
      } as any;

      const result = everyXPlaceholderHelper.get(event, facts);

      expect(result).toStrictEqual([
        { id: '1', type: 'placeholder' },
        { id: '3', type: 'placeholder' },
        { id: '5', type: 'placeholder' },
        { id: '7', type: 'placeholder' },
        { id: '13', type: 'placeholder' },
        { id: '15', type: 'placeholder' },
        { id: '17', type: 'placeholder' },
        { id: '19', type: 'placeholder' },
        { id: '25', type: 'placeholder' },
        { id: '33', type: 'placeholder' }
      ]);
    });
  });
});
